/* =============================================
   IMA NATCON 2025 - Patrons Page
   Extracted from internal styles
   ============================================= */

/* Patrons Main Section */
.patrons-main {
    padding: 80px 0 120px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
    overflow: hidden;
}

.patrons-main::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/assets/icons/pattern-1.jpg') repeat;
    opacity: 0.03;
    z-index: 1;
}

.section-content {
    position: relative;
    z-index: 2;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #0B4C7A 0%, #1e88e5 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    color: white;
    font-size: 32px;
    box-shadow: 0 10px 30px rgba(11, 76, 122, 0.2);
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.section-title {
    font-size: 42px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 16px;
    line-height: 1.2;
}

.section-description {
    font-size: 18px;
    line-height: 1.7;
    color: #5a6c7d;
    max-width: 800px;
    margin: 0 auto;
}

/* Thank You Section */
.thank-you-section {
    margin-top: 80px;
    padding: 60px 40px;
    background: linear-gradient(135deg, #0B4C7A 0%, #1e88e5 100%);
    border-radius: 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.thank-you-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s linear infinite;
}

.thank-you-content {
    position: relative;
    z-index: 2;
}

.thank-you-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    color: white;
    font-size: 32px;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.thank-you-title {
    font-size: 36px;
    font-weight: 700;
    color: white;
    margin-bottom: 16px;
}

.thank-you-message {
    font-size: 18px;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin: 0 auto 32px;
    line-height: 1.6;
}

.thank-you-cta {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: white;
    color: #0B4C7A;
}

.btn-primary:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-outline:hover {
    background: white;
    color: #0B4C7A;
}

/* Patron Categories */
.patron-category {
    margin-bottom: 60px;
}

.category-header {
    text-align: center;
    margin-bottom: 40px;
    position: relative;
}

.category-title {
    display: inline-block;
    font-size: 28px;
    font-weight: 700;
    color: #0B4C7A;
    padding: 0 30px;
    position: relative;
    z-index: 2;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.category-line {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(11, 76, 122, 0.1) 15%, 
        rgba(11, 76, 122, 0.3) 50%, 
        rgba(11, 76, 122, 0.1) 85%, 
        transparent 100%
    );
    z-index: 1;
}

/* Patrons Grid */
.patrons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.patron-card {
    background: #ffffff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.4s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
    border: 1px solid rgba(11, 76, 122, 0.1);
}

.patron-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 12px 30px rgba(11, 76, 122, 0.15);
    border-color: rgba(11, 76, 122, 0.2);
}

.patron-image-container {
    position: relative;
    padding-top: 100%;
    overflow: hidden;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

.patron-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.patron-card:hover .patron-image {
    transform: scale(1.05);
}

.patron-content {
    padding: 24px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.patron-name {
    font-size: 22px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 8px;
    line-height: 1.3;
}

.patron-hospital {
    font-size: 16px;
    font-weight: 600;
    color: #0B4C7A;
    margin-bottom: 8px;
    line-height: 1.4;
}

.patron-location {
    font-size: 14px;
    color: #64748b;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.patron-location i {
    color: #0B4C7A;
    font-size: 14px;
}

.patron-category-badge {
    margin-top: auto;
    background: linear-gradient(135deg, #0B4C7A 0%, #1e88e5 100%);
    color: white;
    font-size: 14px;
    font-weight: 600;
    padding: 8px 16px;
    border-radius: 30px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    align-self: flex-start;
}

.patron-category-badge i {
    font-size: 12px;
}

.patron-contribution {
    font-size: 15px;
    font-weight: 600;
    color: #64748b;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px dashed rgba(11, 76, 122, 0.15);
}

.patron-contribution span {
    color: #0B4C7A;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .patrons-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 24px;
    }
}

@media (max-width: 768px) {
    .section-title {
        font-size: 32px;
    }

    .section-description {
        font-size: 16px;
    }

    .category-title {
        font-size: 24px;
    }

    .patrons-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }

    .patron-content {
        padding: 20px;
    }

    .patron-name {
        font-size: 20px;
    }

    .patron-hospital {
        font-size: 15px;
    }
}

@media (max-width: 576px) {
    .patrons-main {
        padding: 60px 0;
    }

    .section-title {
        font-size: 28px;
    }

    .category-title {
        font-size: 22px;
        padding: 0 20px;
    }

    .patrons-grid {
        grid-template-columns: 1fr;
        max-width: 320px;
        margin-left: auto;
        margin-right: auto;
    }
}
