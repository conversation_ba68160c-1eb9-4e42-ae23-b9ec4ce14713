/* =============================================
   IMA NATCON 2025 - Knowledge Partners Page
   Extracted from internal styles
   ============================================= */

/* Knowledge Partners Main Section */
.knowledge-partners-main {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
}

.knowledge-partners-main::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/assets/icons/pattern-1.jpg') repeat;
    opacity: 0.03;
    z-index: 1;
}

.section-content {
    position: relative;
    z-index: 2;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #0B4C7A 0%, #1e88e5 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    color: white;
    font-size: 32px;
    box-shadow: 0 10px 30px rgba(11, 76, 122, 0.2);
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.section-title {
    font-size: 42px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 16px;
    line-height: 1.2;
}

.section-description {
    font-size: 18px;
    line-height: 1.7;
    color: #5a6c7d;
    max-width: 800px;
    margin: 0 auto;
}

.partners-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 50px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.partner-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #0B4C7A;
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.partner-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(11, 76, 122, 0.02) 0%, rgba(30, 136, 229, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.partner-card:hover::before {
    opacity: 1;
}

.partner-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-left-color: #1e88e5;
}

.partner-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #0B4C7A 0%, #1e88e5 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    color: white;
    font-size: 32px;
    box-shadow: 0 10px 30px rgba(11, 76, 122, 0.2);
    position: relative;
    z-index: 2;
}

.partner-name {
    font-size: 28px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 8px;
    position: relative;
    z-index: 2;
}

.partner-full-name {
    font-size: 16px;
    font-weight: 600;
    color: #0B4C7A;
    margin-bottom: 16px;
    position: relative;
    z-index: 2;
}

.partner-description {
    font-size: 16px;
    line-height: 1.6;
    color: #5a6c7d;
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

.partner-specialties {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
    position: relative;
    z-index: 2;
}

.specialty-tag {
    background: linear-gradient(135deg, #0B4C7A 0%, #1e88e5 100%);
    color: white;
    padding: 6px 14px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .section-title {
        font-size: 32px;
    }

    .section-description {
        font-size: 16px;
    }

    .partners-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .partner-card {
        padding: 24px;
    }

    .partner-icon {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }

    .partner-name {
        font-size: 24px;
    }

    .partner-full-name {
        font-size: 14px;
    }

    .partner-description {
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .knowledge-partners-main {
        padding: 60px 0;
    }

    .section-title {
        font-size: 28px;
    }

    .partner-card {
        padding: 20px;
    }

    .partner-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .partner-name {
        font-size: 20px;
    }

    .specialty-tag {
        font-size: 10px;
        padding: 4px 10px;
    }
}
